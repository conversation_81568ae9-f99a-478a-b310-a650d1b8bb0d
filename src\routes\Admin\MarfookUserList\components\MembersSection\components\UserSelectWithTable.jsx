import React, { useState, useRef, useEffect } from 'react';
import {
  Checkbox,
  ListItemText,
  Popper,
  Box,
  ClickAwayListener,
  Typography,
  IconButton,
  InputBase,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  styled,
  Divider,
} from '@mui/material';
import { ArrowDropDown, Delete } from '@mui/icons-material';
import { useQuery } from 'react-query';
import SearchInput from '../../../../../../routes/Admin/MarfookInstructions/components/MembersSection/components/SearchInput';
import { getOrganizationUsers } from '../../../../../../apis/organization';

const CustomInputBase = styled(InputBase)(() => ({
  border: '1px solid rgba(0,0,0,0.25)',
  borderRadius: 6,
  padding: '6px 8px',
  width: '100%',
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
}));

const CTableHead = styled(TableHead)(() => ({
  '& th': {
    fontSize: '14px',
    color: '#737373',
    border: 'none',
  },
}));

const CTableRow = styled(TableRow)(() => ({
  '& td': {
    border: 0,
    color: '#222222',
    fontSize: '16px',
  },
}));

function UserSelectWithTable({
  selectedMembers = [], setSelectedMembers,
}) {
  const [selectedTexts, setSelectedTexts] = useState([]);
  const [open, setOpen] = useState(false);
  const anchorRef = useRef(null);

  const [members, setMembers] = useState([]);
  const [selectedMembersData, setSelectedMembersData] = useState([]);

  useEffect(() => {
    if (selectedMembers.length === 0) {
      setSelectedTexts([]);
      setSelectedMembersData([]);
    }
  }, [selectedMembers]);

  useQuery({
    queryKey: ['OrganizationUserKey'],
    queryFn: () => getOrganizationUsers(),
    onSuccess: (data) => {
      if (data.status === 200) {
        setMembers(data.data);
      }
    },
  });

  // Update selected members data when selectedMembers changes
  useEffect(() => {
    if (members.length > 0 && selectedMembers.length > 0) {
      const selectedData = members.filter(member => selectedMembers.includes(member.id));
      setSelectedMembersData(selectedData);
      const names = selectedData.map(member => 
        member.first_name !== '' ? `${member.first_name} ${member.last_name}` : member.username
      );
      setSelectedTexts(names);
    }
  }, [members, selectedMembers]);

  const handleChange = (event, value) => {
    setSelectedMembers((prevValues) =>
      (prevValues.includes(value.id)
        ? prevValues.filter((v) => v !== value.id)
        : [...prevValues, value.id]),
    );
  };

  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen);
  };

  const handleClose = (event) => {
    if (anchorRef.current && anchorRef.current.contains(event.target)) {
      return;
    }
    setOpen(false);
  };

  const [searchText, setSearchText] = useState('');
  const [filteredData, setFilteredData] = useState([]);

  useEffect(() => {
    const filtered = members.filter(item => 
      (`${item.first_name?.toLowerCase()}${item.last_name?.toLowerCase()}0${item.username?.toLowerCase()}`).includes(searchText.toLowerCase())
    );
    setFilteredData(filtered);
  }, [searchText, members]);

  const handleSearchChange = (e) => {
    setSearchText(e.target.value);
  };

  const selectAll = () => {
    const allMemberIds = filteredData.map(member => member.id);
    if (selectedMembers.length === filteredData.length) {
      setSelectedMembers([]);
    } else {
      setSelectedMembers(allMemberIds);
    }
  };

  const removeUser = (userId) => {
    setSelectedMembers(prevValues => prevValues.filter(id => id !== userId));
  };

  return (
    <>
      <CustomInputBase
        ref={anchorRef}
        value={selectedTexts.join(', ')}
        onClick={handleToggle}
        endAdornment={(
          <IconButton onClick={handleToggle}>
            <ArrowDropDown />
          </IconButton>
        )}
        placeholder="انتخاب کاربران"
        readOnly
      />
      <Popper
        open={open}
        anchorEl={anchorRef.current}
        placement="bottom-start"
        style={{ width: anchorRef.current ? anchorRef.current.clientWidth : undefined }}
      >
        <ClickAwayListener onClickAway={handleClose}>
          <Box sx={{
            border: 'none',
            bgcolor: 'background.paper',
            width: '100%',
            borderRadius: '8px',
            boxShadow: '0px 4px 20px 0px #0000001A',
          }}
          >
            <Box style={{ minHeight: '200px', maxHeight: '350px', overflowY: 'scroll', padding: '10px' }}>
              <SearchInput label="جستجو" onChange={handleSearchChange} sx={{ position: 'sticky', top: '15px', zIndex: '10' }} />
              {searchText !== '' ? (filteredData.length > 0 ? (
                <>
                  <MenuItem onClick={selectAll}>
                    <Checkbox checked={selectedMembers.length === filteredData.length} />
                    <ListItemText primary="انتخاب همه" />
                  </MenuItem>
                  {filteredData.slice(-10).map((option) => (
                    <MenuItem key={option.id} value={option.id} onClick={(event) => handleChange(event, { id: option.id, value: option.first_name !== '' ? (`${option.first_name} ${option.last_name}`) : option.username })}>
                      <Checkbox checked={selectedMembers.includes(option.id)} />
                      {option.avatar && (
                        <Box sx={{ maxWidth: '40px', mr: 2 }}>
                          <img alt="avatar" width="40px" height="40px" src={option.avatar} style={{ borderRadius: '100px' }} />
                        </Box>
                      )}
                      <ListItemText primary={option.first_name !== '' ? (`${option.first_name} ${option.last_name} (0${option.username})`) : option.username} />
                    </MenuItem>
                  ))}
                </>
              ) : <Box sx={{ width: '100%', textAlign: 'center', py: 6 }}>موردی یافت نشد</Box>) : <Box sx={{ width: '100%', textAlign: 'center', py: 6 }}>نام یا شماره شخص مورد نظر را جستجو کنید</Box>}
            </Box>
          </Box>
        </ClickAwayListener>
      </Popper>

      {selectedMembersData.length > 0 && (
        <>
          <Divider sx={{ mt: 3, mb: 2 }} />
          <Typography sx={{ fontSize: 16, fontWeight: 'bold', mb: 2 }}>
            کاربران انتخاب شده ({selectedMembersData.length})
          </Typography>
          <TableContainer>
            <Table>
              <CTableHead>
                <TableRow>
                  <TableCell>عکس</TableCell>
                  <TableCell>نام کاربر</TableCell>
                  <TableCell>عملیات</TableCell>
                </TableRow>
              </CTableHead>
              <TableBody>
                {selectedMembersData.map(user => (
                  <CTableRow key={user.id}>
                    <TableCell sx={{ maxWidth: '40px' }}>
                      <img
                        width="40px"
                        height="40px"
                        alt="avatar"
                        src={user.avatar || '/logo.png'}
                        style={{ borderRadius: '100px' }}
                      />
                    </TableCell>
                    <TableCell>
                      <span style={{ fontSize: '16px', fontWeight: 'bold' }}>
                        {user.first_name
                          ? `${user.first_name} ${user.last_name}`
                          : user.username}
                      </span>
                      <br />
                      <span style={{ fontSize: '14px' }}>
                        {user.display_username ? `@${user.display_username}` : ''}
                      </span>
                    </TableCell>
                    <TableCell>
                      <IconButton
                        onClick={() => removeUser(user.id)}
                        color="error"
                      >
                        <Delete />
                      </IconButton>
                    </TableCell>
                  </CTableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </>
      )}
    </>
  );
}

export default UserSelectWithTable;
